/ Header Record For PersistentHashMapValueStorage6 5app/src/main/java/com/coffee/erp/CoffeeApplication.kt1 0app/src/main/java/com/coffee/erp/MainActivity.kt2 1app/src/main/java/com/coffee/erp/api/OcrClient.kt3 2app/src/main/java/com/coffee/erp/api/OcrService.kt9 8app/src/main/java/com/coffee/erp/api/model/OcrRequest.kt: 9app/src/main/java/com/coffee/erp/api/model/OcrResponse.kt5 4app/src/main/java/com/coffee/erp/di/NetworkModule.ktB Aapp/src/main/java/com/coffee/erp/network/AnnotationInterceptor.kt7 6app/src/main/java/com/coffee/erp/network/ApiService.kt> =app/src/main/java/com/coffee/erp/network/ApiServiceManager.kt= <app/src/main/java/com/coffee/erp/network/AuthEventManager.kt< ;app/src/main/java/com/coffee/erp/network/AuthInterceptor.ktC Bapp/src/main/java/com/coffee/erp/network/DebouncingErrorHandler.ktB Aapp/src/main/java/com/coffee/erp/network/DebouncingInterceptor.kt; :app/src/main/java/com/coffee/erp/network/JwtInterceptor.kt? >app/src/main/java/com/coffee/erp/network/LoadingInterceptor.kt@ ?app/src/main/java/com/coffee/erp/network/LoadingStateManager.kt: 9app/src/main/java/com/coffee/erp/network/NetworkClient.kt: 9app/src/main/java/com/coffee/erp/network/TokenProvider.ktA @app/src/main/java/com/coffee/erp/network/annotations/Debounce.kt> =app/src/main/java/com/coffee/erp/network/model/ApiResponse.ktD Capp/src/main/java/com/coffee/erp/network/model/PaginatedResponse.ktB Aapp/src/main/java/com/coffee/erp/network/model/auth/AuthModels.ktL Kapp/src/main/java/com/coffee/erp/network/model/beanstore/BeanStoreModels.ktF Eapp/src/main/java/com/coffee/erp/network/model/coffee/CoffeeModels.ktK Japp/src/main/java/com/coffee/erp/network/model/coffee/CoffeeStoreModels.ktK Japp/src/main/java/com/coffee/erp/network/model/coffee/CoffeeStoreSupply.ktS Rapp/src/main/java/com/coffee/erp/network/model/coffee/CoffeeStoreSupplyResponse.ktF Eapp/src/main/java/com/coffee/erp/network/model/common/CommonModels.ktL Kapp/src/main/java/com/coffee/erp/network/model/greenbean/GreenBeanModels.ktJ Iapp/src/main/java/com/coffee/erp/network/model/material/MaterialModels.ktN Mapp/src/main/java/com/coffee/erp/network/model/production/ProductionModels.ktD Capp/src/main/java/com/coffee/erp/network/model/store/StoreModels.kt@ ?app/src/main/java/com/coffee/erp/network/service/AuthService.ktE Dapp/src/main/java/com/coffee/erp/network/service/BeanStoreService.ktB Aapp/src/main/java/com/coffee/erp/network/service/CoffeeService.ktG Fapp/src/main/java/com/coffee/erp/network/service/CoffeeStoreService.ktB Aapp/src/main/java/com/coffee/erp/network/service/CommonService.ktF Eapp/src/main/java/com/coffee/erp/network/service/DepartmentService.ktE Dapp/src/main/java/com/coffee/erp/network/service/GreenBeanService.ktE Dapp/src/main/java/com/coffee/erp/network/service/InventoryService.ktD Capp/src/main/java/com/coffee/erp/network/service/MaterialService.ktF Eapp/src/main/java/com/coffee/erp/network/service/ProductionService.ktA @app/src/main/java/com/coffee/erp/network/service/ShelfService.ktG Fapp/src/main/java/com/coffee/erp/network/service/StockTakingService.ktA @app/src/main/java/com/coffee/erp/network/service/StoreService.ktE Dapp/src/main/java/com/coffee/erp/network/service/WarehouseService.kt? >app/src/main/java/com/coffee/erp/repository/ShelfRepository.ktC Bapp/src/main/java/com/coffee/erp/repository/WarehouseRepository.ktD Capp/src/main/java/com/coffee/erp/ui/components/NumberPickerWheel.kt3 2app/src/main/java/com/coffee/erp/ui/theme/Color.kt3 2app/src/main/java/com/coffee/erp/ui/theme/Theme.kt2 1app/src/main/java/com/coffee/erp/ui/theme/Type.kt5 4app/src/main/java/com/coffee/erp/utils/CameraUtil.kt5 4app/src/main/java/com/coffee/erp/utils/ImageUtils.kt7 6app/src/main/java/com/coffee/erp/utils/QRCodeParser.kt8 7app/src/main/java/com/coffee/erp/utils/UnitConverter.kt/ .app/src/main/java/com/coffee/erp/utils/date.ktI Happ/src/main/java/com/coffee/erp/view/beanstore/BeanStoreDetailScreen.ktG Fapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreFlowScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreFlowViewModel.ktE Dapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreInScreen.ktH Gapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreInViewModel.ktG Fapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreListScreen.ktF Eapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreOutDialog.ktF Eapp/src/main/java/com/coffee/erp/view/beanstore/BeanStoreViewModel.kt; :app/src/main/java/com/coffee/erp/view/chat/AIChatScreen.ktC Bapp/src/main/java/com/coffee/erp/view/coffee/CoffeeDetailScreen.ktA @app/src/main/java/com/coffee/erp/view/coffee/CoffeeFormScreen.ktF Eapp/src/main/java/com/coffee/erp/view/coffee/CoffeeIndexListScreen.ktA @app/src/main/java/com/coffee/erp/view/coffee/CoffeeListScreen.ktB Aapp/src/main/java/com/coffee/erp/view/coffee/CoffeeStoreScreen.kt@ ?app/src/main/java/com/coffee/erp/view/coffee/CoffeeViewModel.ktL Kapp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStatisticsScreen.ktO Napp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStatisticsViewModel.ktL Kapp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreCheckScreen.ktM Lapp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreDetailScreen.ktI Happ/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreInScreen.ktK Japp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreListScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreOutScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/coffeestore/CoffeeStoreViewModel.ktK Japp/src/main/java/com/coffee/erp/view/components/WarehouseShelfCascader.ktG Fapp/src/main/java/com/coffee/erp/view/greenbean/GreenBeanFormScreen.ktL Kapp/src/main/java/com/coffee/erp/view/greenbean/GreenBeanIndexListScreen.ktG Fapp/src/main/java/com/coffee/erp/view/greenbean/GreenBeanListScreen.ktR Qapp/src/main/java/com/coffee/erp/view/handwriting/HandwritingRecognitionScreen.kt= <app/src/main/java/com/coffee/erp/view/home/<USER>/src/main/java/com/coffee/erp/view/inventory/BorrowRecordScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/inventory/InventoryDisplayScreen.ktP Oapp/src/main/java/com/coffee/erp/view/inventory/InventoryHierarchyNavigation.ktL Kapp/src/main/java/com/coffee/erp/view/inventory/InventoryHierarchyScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/inventory/InventoryMaterialItems.ktL Kapp/src/main/java/com/coffee/erp/view/inventory/InventoryNavigationItems.ktR Qapp/src/main/java/com/coffee/erp/view/inventory/InventoryTransactionFormScreen.ktN Mapp/src/main/java/com/coffee/erp/view/inventory/InventoryTransactionScreen.ktI Happ/src/main/java/com/coffee/erp/view/inventory/InventoryUIComponents.ktH Gapp/src/main/java/com/coffee/erp/view/inventory/MaterialDetailScreen.ktM Lapp/src/main/java/com/coffee/erp/view/inventory/MaterialInventoryInfoCard.ktJ Iapp/src/main/java/com/coffee/erp/view/inventory/MaterialOperationPanel.ktM Lapp/src/main/java/com/coffee/erp/view/inventory/MaterialSelectorWithIndex.ktP Oapp/src/main/java/com/coffee/erp/view/inventory/MaterialTransactionLogScreen.ktE Dapp/src/main/java/com/coffee/erp/view/inventory/ShelfDetailScreen.ktE Dapp/src/main/java/com/coffee/erp/view/inventory/StockTakingScreen.ktM Lapp/src/main/java/com/coffee/erp/view/inventory/TransactionListComponents.ktI Happ/src/main/java/com/coffee/erp/view/inventory/WarehouseDetailScreen.kt; :app/src/main/java/com/coffee/erp/view/login/LoginScreen.kt9 8app/src/main/java/com/coffee/erp/view/main/MainScreen.ktG Fapp/src/main/java/com/coffee/erp/view/material/DepartmentFormScreen.ktM Lapp/src/main/java/com/coffee/erp/view/material/DepartmentManagementScreen.ktE Dapp/src/main/java/com/coffee/erp/view/material/MaterialFormScreen.ktJ Iapp/src/main/java/com/coffee/erp/view/material/MaterialIndexListScreen.ktE Dapp/src/main/java/com/coffee/erp/view/material/MaterialListScreen.ktK Japp/src/main/java/com/coffee/erp/view/material/MaterialManagementScreen.ktI Happ/src/main/java/com/coffee/erp/view/material/MaterialSelectorScreen.ktB Aapp/src/main/java/com/coffee/erp/view/material/ShelfFormScreen.ktH Gapp/src/main/java/com/coffee/erp/view/material/ShelfManagementScreen.ktF Eapp/src/main/java/com/coffee/erp/view/material/WarehouseFormScreen.ktL Kapp/src/main/java/com/coffee/erp/view/material/WarehouseManagementScreen.kt< ;app/src/main/java/com/coffee/erp/view/navigation/AppBars.ktA @app/src/main/java/com/coffee/erp/view/navigation/CoffeeRoutes.ktD Capp/src/main/java/com/coffee/erp/view/navigation/GreenBeanRoutes.kt? >app/src/main/java/com/coffee/erp/view/navigation/HomeRoutes.ktD Capp/src/main/java/com/coffee/erp/view/navigation/InventoryRoutes.ktC Bapp/src/main/java/com/coffee/erp/view/navigation/MaterialRoutes.ktH Gapp/src/main/java/com/coffee/erp/view/navigation/NavigationContainer.kt; :app/src/main/java/com/coffee/erp/view/navigation/Screen.kt@ ?app/src/main/java/com/coffee/erp/view/navigation/StoreRoutes.kt@ ?app/src/main/java/com/coffee/erp/view/navigation/TasksRoutes.ktC Bapp/src/main/java/com/coffee/erp/view/navigation/WorkshopRoutes.kt7 6app/src/main/java/com/coffee/erp/view/ocr/OcrScreen.ktG Fapp/src/main/java/com/coffee/erp/view/ocr/components/CameraControls.ktF Eapp/src/main/java/com/coffee/erp/view/ocr/components/CameraPreview.ktJ Iapp/src/main/java/com/coffee/erp/view/ocr/components/ResultBottomSheet.ktJ Iapp/src/main/java/com/coffee/erp/view/ocr/components/ScanningAnimation.ktF Eapp/src/main/java/com/coffee/erp/view/ocr/components/SelectableTag.ktC Bapp/src/main/java/com/coffee/erp/view/product/ProductListScreen.kt? >app/src/main/java/com/coffee/erp/view/profile/ProfileScreen.ktF Eapp/src/main/java/com/coffee/erp/view/scanner/ExampleScannerUseage.ktN Mapp/src/main/java/com/coffee/erp/view/scanner/SampleCustomScannerComponent.kt? >app/src/main/java/com/coffee/erp/view/scanner/ScannerScreen.ktP Oapp/src/main/java/com/coffee/erp/view/scanner/material/MaterialScannerScreen.kt= <app/src/main/java/com/coffee/erp/view/splash/SplashScreen.ktE Dapp/src/main/java/com/coffee/erp/view/statistics/StatisticsScreen.ktL Kapp/src/main/java/com/coffee/erp/view/store/StoreApplicationDetailScreen.ktO Napp/src/main/java/com/coffee/erp/view/store/StoreApplicationDetailViewModel.ktJ Iapp/src/main/java/com/coffee/erp/view/store/StoreApplicationListScreen.ktI Happ/src/main/java/com/coffee/erp/view/store/StoreApplicationViewModel.kt; :app/src/main/java/com/coffee/erp/view/tasks/TasksScreen.ktE Dapp/src/main/java/com/coffee/erp/view/workshop/ProductionTaskCard.ktM Lapp/src/main/java/com/coffee/erp/view/workshop/ProductionTaskDetailScreen.ktK Japp/src/main/java/com/coffee/erp/view/workshop/ProductionTaskFormScreen.ktA @app/src/main/java/com/coffee/erp/view/workshop/WorkshopScreen.ktF Eapp/src/main/java/com/coffee/erp/viewmodel/DepartmentFormViewModel.ktF Eapp/src/main/java/com/coffee/erp/viewmodel/DepartmentListViewModel.ktE Dapp/src/main/java/com/coffee/erp/viewmodel/GreenBeanFormViewModel.ktA @app/src/main/java/com/coffee/erp/viewmodel/InventoryViewModel.ktD Capp/src/main/java/com/coffee/erp/viewmodel/MaterialFormViewModel.ktE Dapp/src/main/java/com/coffee/erp/viewmodel/MaterialIndexViewModel.ktD Capp/src/main/java/com/coffee/erp/viewmodel/MaterialListViewModel.ktB Aapp/src/main/java/com/coffee/erp/viewmodel/ProductionViewModel.ktC Bapp/src/main/java/com/coffee/erp/viewmodel/StockTakingViewModel.ktF Eapp/src/main/java/com/coffee/erp/viewmodel/material/ShelfViewModel.ktJ Iapp/src/main/java/com/coffee/erp/viewmodel/material/WarehouseViewModel.ktA @app/src/main/java/com/coffee/erp/view/workshop/WorkshopScreen.ktL Kapp/src/main/java/com/coffee/erp/view/inventory/InventoryHierarchyScreen.ktC Bapp/src/main/java/com/coffee/erp/view/navigation/MaterialRoutes.kt