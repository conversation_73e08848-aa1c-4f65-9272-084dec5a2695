package com.coffee.erp.view.inventory

import android.util.Log
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Inventory
import androidx.compose.material.icons.filled.ListAlt
import androidx.compose.material.icons.filled.ViewModule
import androidx.compose.material.icons.filled.Warehouse
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.coffee.erp.view.navigation.Screen
import com.coffee.erp.viewmodel.InventoryViewModel
import com.coffee.erp.viewmodel.WarehouseDetailState
import com.coffee.erp.viewmodel.WarehouseSummariesState
import kotlinx.coroutines.flow.collectLatest

/**
 * 库存层级显示主界面 - 手风琴式布局
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InventoryHierarchyScreen(
    onNavigateBack: () -> Unit,
    onNavigateToWarehouse: (warehouseId: Int) -> Unit,
    navController: NavController? = null,
    modifier: Modifier = Modifier,
    viewModel: InventoryViewModel = hiltViewModel()
) {
    val warehouseSummariesState by viewModel.warehouseSummariesState.collectAsState()
    val warehouseDetailState by viewModel.warehouseDetailState.collectAsState()
    val materialNameFilter by viewModel.materialNameFilter.collectAsState()

    // 加载仓库列表数据
    LaunchedEffect(Unit) {
        viewModel.loadAllWarehouseSummaries()
    }

    // 当前选中的仓库ID
    var selectedWarehouseId by remember { mutableStateOf<Int?>(null) }

    // 当前选中的货架名称
    var selectedShelfName by remember { mutableStateOf<String?>(null) }

    // 待解析的货架ID（从MaterialTransactionLog返回时使用）
    var pendingShelfId by remember { mutableStateOf<Int?>(null) }

    // 过滤弹窗状态
    var showFilterDialog by remember { mutableStateOf(false) }

    // 临时保存过滤条件
    var tempMaterialNameFilter by remember { mutableStateOf(materialNameFilter) }

    // 处理从TransactionForm和MaterialTransactionLog返回的导航参数
    if (navController != null) {
        LaunchedEffect(Unit) {
            try {
                // 从savedStateHandle中获取返回参数
                val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
                val returnToWarehouseId = savedStateHandle?.get<Int>("returnToWarehouseId")
                val returnToShelfName = savedStateHandle?.get<String>("returnToShelfName")
                val returnToShelfId = savedStateHandle?.get<Int>("returnToShelfId")

                // 打印日志，查看是否成功获取参数
                Log.d(
                    "InventoryHierarchy",
                    "从savedStateHandle获取返回参数 - 仓库ID: $returnToWarehouseId, 货架名称: $returnToShelfName, 货架ID: $returnToShelfId"
                )

                // 如果参数有效，更新选中的仓库和货架
                if (returnToWarehouseId != null) {
                    selectedWarehouseId = returnToWarehouseId

                    // 优先使用货架名称，如果没有货架名称但有货架ID，则需要从仓库详情中查找对应的货架名称
                    if (returnToShelfName != null) {
                        selectedShelfName = returnToShelfName
                    } else if (returnToShelfId != null) {
                        // 需要等待仓库详情加载完成后，根据货架ID查找货架名称
                        // 这里先设置一个标记，在仓库详情加载完成后再处理
                        pendingShelfId = returnToShelfId
                        Log.d(
                            "InventoryHierarchy",
                            "需要根据货架ID查找货架名称: $returnToShelfId"
                        )
                    }

                    // 移除参数，防止再次进入时重复读取
                    savedStateHandle.remove<Int>("returnToWarehouseId")
                    savedStateHandle.remove<String>("returnToShelfName")
                    savedStateHandle.remove<Int>("returnToShelfId")

                    // 记录日志
                    Log.d(
                        "InventoryHierarchy",
                        "成功恢复之前的选择状态 - 仓库ID: $selectedWarehouseId, 货架名称: $selectedShelfName"
                    )
                }
            } catch (e: Exception) {
                // 记录异常信息
                Log.e("InventoryHierarchy", "读取返回参数失败: ${e.message}", e)
            }
        }

        // 正确监听过滤弹窗触发 - 使用 collectLatest
        LaunchedEffect(Unit) {
            try {
                navController.currentBackStackEntry?.savedStateHandle?.getStateFlow<Boolean>(
                    "showFilterDialog",
                    false
                )?.collectLatest { shouldShowFilter ->
                    if (shouldShowFilter) {
                        // 显示过滤弹窗
                        showFilterDialog = true
                        // 初始化临时过滤条件
                        tempMaterialNameFilter = materialNameFilter
                        // 重置触发标志
                        navController.currentBackStackEntry?.savedStateHandle?.set(
                            "showFilterDialog",
                            false
                        )
                        // 记录日志
                        Log.d("InventoryHierarchy", "通过 savedStateHandle 触发过滤弹窗")
                    }
                }
            } catch (e: Exception) {
                Log.e("InventoryHierarchy", "监听过滤弹窗触发失败: ${e.message}", e)
            }
        }
    }

    // 加载选中仓库的详情
    LaunchedEffect(selectedWarehouseId, materialNameFilter) {
        selectedWarehouseId?.let {
            // 加载仓库详情（不清除货架选择，以支持从表单返回时保留选中状态）
            viewModel.loadWarehouseDetail(it)
        }
    }

    // 处理待解析的货架ID - 当仓库详情加载成功后，根据货架ID查找货架名称
    LaunchedEffect(warehouseDetailState, pendingShelfId) {
        if (pendingShelfId != null && warehouseDetailState is WarehouseDetailState.Success) {
            val warehouseData = (warehouseDetailState as WarehouseDetailState.Success).data
            val shelf = warehouseData.shelves.find { it.id == pendingShelfId }
            if (shelf != null) {
                selectedShelfName = shelf.name
                Log.d(
                    "InventoryHierarchy",
                    "成功根据货架ID $pendingShelfId 找到货架名称: ${shelf.name}"
                )
            } else {
                Log.w(
                    "InventoryHierarchy",
                    "未能根据货架ID $pendingShelfId 找到对应的货架"
                )
            }
            // 清除待解析的货架ID
            pendingShelfId = null
        }
    }

    // 获取屏幕宽度
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp

    // 计算左侧导航栏宽度为屏幕宽度的25%
    val navigationWidth = screenWidth * 0.3f

    // 过滤弹窗
    if (showFilterDialog) {
        AlertDialog(
            onDismissRequest = { showFilterDialog = false },
            title = { Text("过滤物料") },
            text = {
                Column {
                    OutlinedTextField(
                        value = tempMaterialNameFilter,
                        onValueChange = { tempMaterialNameFilter = it },
                        label = { Text("物料名称") },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )

                    if (tempMaterialNameFilter.isNotEmpty()) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp),
                            horizontalArrangement = Arrangement.End,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            TextButton(onClick = {
                                tempMaterialNameFilter = ""
                            }) {
                                Text("清除")
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = {
                    // 应用过滤条件
                    viewModel.setMaterialNameFilter(tempMaterialNameFilter)
                    // 关闭弹窗
                    showFilterDialog = false
                    // 添加日志
                    Log.d("InventoryHierarchy", "应用过滤条件: $tempMaterialNameFilter")
                }) {
                    Text("确定")
                }
            },
            dismissButton = {
                TextButton(onClick = {
                    // 取消操作，恢复原有过滤条件
                    tempMaterialNameFilter = materialNameFilter
                    showFilterDialog = false
                }) {
                    Text("取消")
                }
            }
        )
    }

    Scaffold { paddingValues ->
        Row(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 左侧导航区域 - 显示仓库和货架
            Box(
                modifier = Modifier
                    .width(navigationWidth)
                    .fillMaxHeight()
                    .background(Color(0xFFF5F5F5))
            ) {
                when (val state = warehouseSummariesState) {
                    is WarehouseSummariesState.Initial -> {
                        // 初始状态，不显示内容
                    }

                    is WarehouseSummariesState.Loading -> {
                        // 加载中
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(24.dp),
                                strokeWidth = 2.dp
                            )
                        }
                    }

                    is WarehouseSummariesState.Error -> {
                        // 错误状态
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(24.dp)
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                Text(
                                    text = "加载失败: ${state.message}",
                                    color = MaterialTheme.colorScheme.error
                                )

                                Spacer(modifier = Modifier.height(8.dp))

                                TextButton(
                                    onClick = { viewModel.loadAllWarehouseSummaries() }
                                ) {
                                    Text("重试")
                                }
                            }
                        }
                    }

                    is WarehouseSummariesState.Success -> {
                        if (state.data.isEmpty()) {
                            // 空数据状态
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.Inventory,
                                        contentDescription = null,
                                        tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                                        modifier = Modifier.size(36.dp)
                                    )

                                    Spacer(modifier = Modifier.height(8.dp))

                                    Text(
                                        text = "暂无仓库数据",
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        } else {
                            // 两级导航：仓库和货架
                            LazyColumn(
                                contentPadding = PaddingValues(vertical = 12.dp, horizontal = 2.dp),
                                verticalArrangement = Arrangement.spacedBy(8.dp)
                            ) {
                                items(state.data) { warehouse ->
                                    val isSelected = warehouse.id == selectedWarehouseId

                                    // 仓库导航项
                                    WarehouseNavigationItem(
                                        warehouse = warehouse,
                                        isSelected = isSelected,
                                        onClick = { warehouseId ->
                                            // 只有当选择的是不同的仓库时，才重置货架选择
                                            if (selectedWarehouseId != warehouseId) {
                                                // 记录日志
                                                Log.d(
                                                    "InventoryHierarchy",
                                                    "手动选择了新的仓库 ID: $warehouseId，重置货架选择"
                                                )
                                                selectedWarehouseId = warehouseId
                                                // 重置货架选择
                                                selectedShelfName = null
                                            } else {
                                                // 如果是点击了同一个仓库，只更新仓库ID而不清除货架选择
                                                selectedWarehouseId = warehouseId
                                            }
                                        }
                                    )

                                    // 显示选中仓库的货架列表
                                    if (isSelected && warehouseDetailState is WarehouseDetailState.Success) {
                                        val warehouseData =
                                            (warehouseDetailState as WarehouseDetailState.Success).data

                                        // 货架列表 - 始终显示所有货架
                                        warehouseData.shelves.forEach { shelfInventory ->
                                            // 使用货架名称判断是否选中
                                            val isShelfSelected =
                                                shelfInventory.name == selectedShelfName
                                            // 获取货架ID
                                            val shelfId = shelfInventory.id

                                            ShelfNavigationItem(
                                                name = shelfInventory.name,
                                                itemCount = shelfInventory.items_count,
                                                shelfId = shelfId,
                                                isSelected = isShelfSelected,
                                                onClick = { name, id ->
                                                    // 记录日志
                                                    Log.d(
                                                        "InventoryHierarchy",
                                                        "选择货架: $name, ID: $id, 仓库ID: $selectedWarehouseId"
                                                    )
                                                    // 使用货架名称和ID进行选择
                                                    selectedShelfName = name
                                                }
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 右侧内容区域 - 显示物料列表
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
            ) {
                when {
                    // 未选择仓库
                    selectedWarehouseId == null -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Warehouse,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.primary.copy(alpha = 0.5f),
                                    modifier = Modifier.size(64.dp)
                                )

                                Spacer(modifier = Modifier.height(16.dp))

                                Text(
                                    text = "请从左侧选择仓库",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        }
                    }

                    // 仓库详情加载中
                    warehouseDetailState is WarehouseDetailState.Loading -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }

                    // 仓库详情加载失败
                    warehouseDetailState is WarehouseDetailState.Error -> {
                        val errorMessage =
                            (warehouseDetailState as WarehouseDetailState.Error).message
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Error,
                                    contentDescription = null,
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(48.dp)
                                )

                                Spacer(modifier = Modifier.height(16.dp))

                                Text(
                                    text = "加载失败: $errorMessage",
                                    color = MaterialTheme.colorScheme.error
                                )

                                Spacer(modifier = Modifier.height(16.dp))

                                Button(
                                    onClick = {
                                        selectedWarehouseId?.let { viewModel.loadWarehouseDetail(it) }
                                    }
                                ) {
                                    Text("重试")
                                }
                            }
                        }
                    }

                    // 仓库详情加载成功
                    warehouseDetailState is WarehouseDetailState.Success -> {
                        val warehouseData =
                            (warehouseDetailState as WarehouseDetailState.Success).data

                        // 物料列表视图
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = 12.dp, vertical = 8.dp)
                        ) {
                            // 仓库/货架标题
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(bottom = 12.dp),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = warehouseData.warehouse_info.name +
                                            // 使用 selectedShelfName 显示标题
                                            (selectedShelfName?.let { " > $it" } ?: ""),
                                    style = MaterialTheme.typography.titleSmall,
                                    fontWeight = FontWeight.SemiBold,
                                    modifier = Modifier.weight(1f)
                                )

                                // 显示当前过滤条件
                                if (materialNameFilter.isNotEmpty()) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .padding(horizontal = 4.dp)
                                            .clip(RoundedCornerShape(4.dp))
                                            .background(
                                                MaterialTheme.colorScheme.primaryContainer.copy(
                                                    alpha = 0.7f
                                                )
                                            )
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "过滤: $materialNameFilter",
                                            style = MaterialTheme.typography.labelSmall,
                                            color = MaterialTheme.colorScheme.onPrimaryContainer
                                        )

                                        Spacer(modifier = Modifier.width(4.dp))

                                        IconButton(
                                            onClick = {
                                                // 清除过滤条件
                                                viewModel.setMaterialNameFilter("")
                                                Log.d("InventoryHierarchy", "清除过滤条件")
                                            },
                                            modifier = Modifier.size(16.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Clear,
                                                contentDescription = "清除过滤",
                                                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                                                modifier = Modifier.size(12.dp)
                                            )
                                        }
                                    }
                                }

                                // 仅在选中货架时才显示"新增物料"按钮
                                if (selectedShelfName != null) {
                                    OutlinedButton(
                                        onClick = {
                                            try {
                                                // 打印调试信息
                                                Log.d(
                                                    "InventoryHierarchy",
                                                    "新增物料按钮点击 - 仓库ID: $selectedWarehouseId, 货架名称: $selectedShelfName"
                                                )

                                                // 构建导航路由并执行导航
                                                selectedWarehouseId?.let { warehouseId ->
                                                    // 从选中的货架中获取对应的货架信息
                                                    val selectedShelf =
                                                        warehouseData.shelves.find { it.name == selectedShelfName }
                                                    // 获取货架ID，确保不为null
                                                    val selectedShelfId = selectedShelf?.id

                                                    // 添加更详细的日志，帮助调试
                                                    if (selectedShelfId == null) {
                                                        Log.e(
                                                            "InventoryHierarchy",
                                                            "错误：未能获取到有效的货架ID！"
                                                        )
                                                        Log.d(
                                                            "InventoryHierarchy",
                                                            "货架信息：${selectedShelf?.toString()}"
                                                        )
                                                        Log.d(
                                                            "InventoryHierarchy",
                                                            "shelf_info：${selectedShelf?.shelf_info}"
                                                        )
                                                    }

                                                    // 记录日志
                                                    Log.d(
                                                        "InventoryHierarchy",
                                                        "货架ID: $selectedShelfId 货架名称: $selectedShelfName 物料数: ${selectedShelf?.items_count ?: 0}"
                                                    )

                                                    // 使用Screen.TransactionForm的createRoute方法创建正确的路由
                                                    val route = if (selectedShelfId != null) {
                                                        // 如果能找到货架ID，则添加到URL参数中
                                                        Screen.TransactionForm.createRoute(
                                                            warehouseId = warehouseId,
                                                            warehouseName = warehouseData.warehouse_info.name,
                                                            shelfName = selectedShelfName,
                                                            transactionType = "IN",
                                                            targetShelfId = selectedShelfId
                                                        )
                                                    } else {
                                                        // 如果找不到货架ID，使用从货架列表中查找或生成一个默认ID
                                                        // 尝试从所有货架中查找匹配名称的货架
                                                        val foundShelf =
                                                            warehouseData.shelves.find { it.name == selectedShelfName }
                                                        val defaultShelfId = foundShelf?.id ?: 0

                                                        Log.w(
                                                            "InventoryHierarchy",
                                                            "未找到直接的货架ID，使用备用ID: $defaultShelfId"
                                                        )

                                                        // 使用备用ID创建路由
                                                        Screen.TransactionForm.createRoute(
                                                            warehouseId = warehouseId,
                                                            warehouseName = warehouseData.warehouse_info.name,
                                                            shelfName = selectedShelfName,
                                                            transactionType = "IN",
                                                            targetShelfId = defaultShelfId
                                                        )
                                                    }

                                                    // 执行导航
                                                    navController?.navigate(route)

                                                    // 记录成功日志
                                                    Log.d(
                                                        "InventoryHierarchy",
                                                        "导航成功，参数已传递，route: $route"
                                                    )
                                                }
                                            } catch (e: Exception) {
                                                // 记录异常信息
                                                Log.e(
                                                    "InventoryHierarchy",
                                                    "导航失败: ${e.message}",
                                                    e
                                                )
                                                // 可以在这里显示错误提示，比如Toast或Snackbar
                                            }
                                        },
                                        shape = RoundedCornerShape(2.dp),
                                        contentPadding = PaddingValues(
                                            horizontal = 6.dp,
                                            vertical = 2.dp
                                        ),
                                        border = BorderStroke(
                                            0.5.dp,
                                            MaterialTheme.colorScheme.primary
                                        ),
                                        modifier = Modifier.height(24.dp)
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.Add,
                                            contentDescription = "添加物料",
                                            modifier = Modifier.size(12.dp)
                                        )
                                        Spacer(modifier = Modifier.width(2.dp))
                                        Text(
                                            text = "新增物料",
                                            style = MaterialTheme.typography.labelSmall.copy(
                                                fontSize = 10.sp
                                            )
                                        )
                                    }
                                }
                            }

                            // 物料列表
                            if (selectedShelfName != null) {
                                // 显示选中货架的物料
                                // 查找对应的货架信息
                                val selectedShelf =
                                    warehouseData.shelves.find { it.name == selectedShelfName }
                                val items = selectedShelf?.inventory ?: emptyList()

                                if (items.isEmpty()) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .weight(1f),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "该货架暂无物料",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                } else {
                                    LazyColumn(
                                        modifier = Modifier.fillMaxSize(),
                                        verticalArrangement = Arrangement.spacedBy(0.dp)
                                    ) {
                                        items(items) { item ->
                                            MaterialItemCard(
                                                inventoryDetail = item,
                                                warehouseId = item.warehouse_id,
                                                shelfId = selectedShelf?.id ?: 0,
                                                onClick = { warehouseId, shelfId, materialId ->
                                                    // 导航到物料交易日志页面
                                                    if (navController != null) {
                                                        // 先加载物料详情信息
                                                        viewModel.loadMaterialDetail(
                                                            warehouseId,
                                                            shelfId,
                                                            materialId
                                                        )

                                                        // 导航到物料交易记录页面
                                                        navController.navigate(
                                                            Screen.MaterialTransactionLog.createRoute(
                                                                materialId = materialId,
                                                                warehouseId = warehouseId,
                                                                shelfId = shelfId
                                                            )
                                                        )
                                                    } else {
                                                        // 如果navController为空，退回到通用的导航回调
                                                        onNavigateToWarehouse(warehouseId)
                                                    }
                                                }
                                            )
                                            // 添加虚线分割线（对最后一项不添加）
                                            if (item != items.last()) {
                                                DashedDivider(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .padding(horizontal = 8.dp),
                                                    color = MaterialTheme.colorScheme.outline.copy(
                                                        alpha = 0.3f
                                                    ),
                                                    thickness = 1.dp,
                                                    dashWidth = 5.dp,
                                                    dashGap = 3.dp
                                                )
                                            }
                                        }
                                    }
                                }
                            } else {
                                // 显示仓库总览
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 2.dp)
                                        .clip(RoundedCornerShape(4.dp))
                                        .border(
                                            width = 0.5.dp,
                                            color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f),
                                            shape = RoundedCornerShape(4.dp)
                                        ),
                                    colors = CardDefaults.cardColors(
                                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(
                                            alpha = 0.7f
                                        )
                                    ),
                                    elevation = CardDefaults.cardElevation(
                                        defaultElevation = 0.dp
                                    )
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(8.dp)
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier.padding(bottom = 4.dp)
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Warehouse,
                                                contentDescription = null,
                                                tint = MaterialTheme.colorScheme.primary,
                                                modifier = Modifier.size(12.dp)
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            Text(
                                                text = "仓库总览",
                                                style = MaterialTheme.typography.labelMedium,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                fontWeight = FontWeight.Medium
                                            )
                                        }

                                        Spacer(modifier = Modifier.height(2.dp))

                                        // 添加分隔线
                                        Divider(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 2.dp),
                                            thickness = 0.5.dp,
                                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f)
                                        )

                                        Spacer(modifier = Modifier.height(4.dp))

                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            horizontalArrangement = Arrangement.SpaceAround
                                        ) {
                                            // 物料数量
                                            InfoCard(
                                                icon = Icons.Default.Inventory,
                                                value = warehouseData.total_items.toString(),
                                                label = "物料数量"
                                            )

                                            // 分隔线
                                            Box(
                                                modifier = Modifier
                                                    .height(35.dp)
                                                    .width(0.5.dp)
                                                    .background(
                                                        MaterialTheme.colorScheme.outline.copy(
                                                            alpha = 0.2f
                                                        )
                                                    )
                                            )

                                            // 货架数量
                                            InfoCard(
                                                icon = Icons.Default.ViewModule,
                                                value = warehouseData.shelves_count.toString(),
                                                label = "货架数量"
                                            )
                                        }
                                    }
                                }

                                Spacer(modifier = Modifier.height(12.dp))

                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier.padding(bottom = 4.dp)
                                ) {
                                    Icon(
                                        imageVector = Icons.Default.ListAlt,
                                        contentDescription = null,
                                        tint = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier.size(12.dp)
                                    )
                                    Spacer(modifier = Modifier.width(4.dp))
                                    Text(
                                        text = "物料总览",
                                        style = MaterialTheme.typography.labelMedium,
                                        color = MaterialTheme.colorScheme.onSurface,
                                        fontWeight = FontWeight.Medium
                                    )
                                }

                                // 添加分隔线
                                Divider(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 2.dp, horizontal = 0.dp),
                                    thickness = 0.5.dp,
                                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f)
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                // 显示所有物料
                                // 将所有货架的物料合并为一个列表
                                val allItems = warehouseData.shelves.flatMap { it.inventory }

                                if (allItems.isEmpty()) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .weight(1f),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = "该仓库暂无物料",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                } else {
                                    LazyColumn(
                                        modifier = Modifier.fillMaxSize(),
                                        verticalArrangement = Arrangement.spacedBy(0.dp)
                                    ) {
                                        items(allItems) { item ->
                                            // 查找物料所在的货架
                                            val shelfInventory =
                                                warehouseData.shelves.find { shelf ->
                                                    shelf.inventory.any { it.material_id == item.material_id }
                                                }
                                            val shelfName = shelfInventory?.name
                                            val shelfId = shelfInventory?.id ?: 0

                                            MaterialItemCard(
                                                inventoryDetail = item,
                                                warehouseId = item.warehouse_id,
                                                shelfId = shelfId,
                                                onClick = { warehouseId, shelfId, materialId ->
                                                    // 导航到物料交易日志页面
                                                    if (navController != null) {
                                                        // 先加载物料详情信息
                                                        viewModel.loadMaterialDetail(
                                                            warehouseId,
                                                            shelfId,
                                                            materialId
                                                        )

                                                        // 导航到物料交易记录页面
                                                        navController.navigate(
                                                            Screen.MaterialTransactionLog.createRoute(
                                                                materialId = materialId,
                                                                warehouseId = warehouseId,
                                                                shelfId = shelfId
                                                            )
                                                        )
                                                    } else {
                                                        // 如果navController为空，退回到通用的导航回调
                                                        onNavigateToWarehouse(warehouseId)
                                                    }
                                                }
                                            )
                                            // 添加虚线分割线（对最后一项不添加）
                                            if (item != allItems.last()) {
                                                DashedDivider(
                                                    modifier = Modifier
                                                        .fillMaxWidth()
                                                        .padding(horizontal = 8.dp),
                                                    color = MaterialTheme.colorScheme.outline.copy(
                                                        alpha = 0.3f
                                                    ),
                                                    thickness = 1.dp,
                                                    dashWidth = 5.dp,
                                                    dashGap = 3.dp
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


