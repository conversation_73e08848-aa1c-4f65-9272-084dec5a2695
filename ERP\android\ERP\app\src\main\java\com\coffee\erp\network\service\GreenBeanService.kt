package com.coffee.erp.network.service

import com.coffee.erp.view.greenbean.GreenBeanResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface GreenBeanService {
    @GET("greenbean/list/")
    suspend fun getGreenBeanList(): Response<GreenBeanResponse>

    @POST("greenbean/")
    suspend fun createGreenBean(@Body requestBody: Map<String, String>): Response<Unit>
} 