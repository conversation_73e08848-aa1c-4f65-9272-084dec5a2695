package com.coffee.erp.view.greenbean

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.coffee.erp.network.LoadingStateManager
import com.coffee.erp.network.NetworkClient

data class GreenBean(
    val id: Int,
    val bean_id: String,
    val bean_name: String,
    val comment: String,
    val bean_stock: List<BeanStock> = emptyList()
)

data class BeanStock(
    val id: Int,
    val stock_code: String,
    val bean_id: String,
    val location: String,
    val qrcode: String,
    val quantity: Double,
    val threshold: Double,
    val unit: String,
    val comment: String,
    val operator_id: Int,
    val updated_at: String
)

data class GreenBeanResponse(
    val code: Int,
    val data: GreenBeanData,
    val msg: String
)

data class GreenBeanData(
    val list: List<GreenBean>,
    val total: Int
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GreenBeanListScreen(
    onNavigateBack: () -> Unit
) {
    var greenBeans by remember { mutableStateOf<List<GreenBean>>(emptyList()) }
    var error by remember { mutableStateOf<String?>(null) }
    val context = LocalContext.current

    val isLoading by LoadingStateManager.isLoading.collectAsState()

    LaunchedEffect(Unit) {
        try {
            val response = NetworkClient.create(context)
                .getGreenBeanList()
            if (response.isSuccessful && response.body()?.code == 200) {
                greenBeans = response.body()?.data?.list ?: emptyList()
            } else {
                error = "Failed to load green beans: ${response.message()}"
            }
        } catch (e: Exception) {
            error = e.message
            e.printStackTrace()
        }
    }

    Scaffold { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            when {
                error != null -> {
                    Text("Error: $error")
                }

                else -> {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        contentPadding = PaddingValues(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(greenBeans) { bean ->
                            GreenBeanCard(bean)
                        }
                    }
                }
            }

            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GreenBeanCard(bean: GreenBean) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = bean.bean_name,
                style = MaterialTheme.typography.titleMedium
            )
            Spacer(Modifier.height(4.dp))
            Text(
                text = "编号: ${bean.bean_id}",
                style = MaterialTheme.typography.bodyMedium
            )
            if (bean.comment.isNotEmpty()) {
                Text(
                    text = "备注: ${bean.comment}",
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // Display stock information
            if (bean.bean_stock.isNotEmpty()) {
                Spacer(Modifier.height(8.dp))
                Divider()
                Spacer(Modifier.height(8.dp))
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            color = Color(0xFFE3F2FD),
                            shape = MaterialTheme.shapes.small
                        )
                        .padding(12.dp)
                ) {
                    Text(
                        text = "库存信息:",
                        style = MaterialTheme.typography.titleSmall
                    )
                    Spacer(Modifier.height(4.dp))
                    bean.bean_stock.forEach { stock ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                Text(
                                    text = "库存编号: ${stock.stock_code}",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Text(
                                    text = "位置: ${stock.location}",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                Text(
                                    text = "数量: ${stock.quantity} ${stock.unit}",
                                    style = MaterialTheme.typography.bodyMedium
                                )
                                if (stock.threshold >= 0) {
                                    Text(
                                        text = "预警值: ${stock.threshold} ${stock.unit}",
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                        if (bean.bean_stock.last() != stock) {
                            Spacer(Modifier.height(8.dp))
                            Divider(modifier = Modifier.padding(vertical = 4.dp))
                        }
                    }
                }
            }
        }
    }
} 