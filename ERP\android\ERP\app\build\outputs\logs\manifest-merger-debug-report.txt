-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:36:9-44:20
	android:grantUriPermissions
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:40:13-47
	android:authorities
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:38:13-64
	android:exported
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:39:13-37
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:37:13-62
manifest
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:2:1-46:12
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:2:1-46:12
MERGED from [OcrLibrary.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b97644a16d8262412c5844c09cb1dad\transformed\OcrLibrary\AndroidManifest.xml:2:1-8:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff5604a0cf08c8de7ced63fdd0973655\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\54acd67fb2220d5536a3b4d914012cde\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9495f7808b4156f18b29808caae1606f\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ed2fadd80d6945045f8a5a3b9b067b3\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5be5ade89f44101ec0e491bb2962fd5\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\7d72b964d4495d7859d0b78c38bb2d24\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287a845c912e32b90d9cbfc348ad34a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c05fab69a16c9f81abcb338745b482e\transformed\navigation-compose-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fe45853e0c81b999cf9655bea812e7a\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2081bd59573ce5b579d42acdf887b9b2\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4bee85f02b9349668dbe3bc2f89ef42\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\00ac1d7733b4dcee47cf92051b0baf0e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b2c42d0ce84020feb69aadd2f21e791\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\96008b571c873a273cfaafc6b8ced783\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bc541a45dfd0e62233c7bedb463d5b7\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d5ce7551cf679fefeef255e3a3ff4b9\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\595ec66ace766be95db48df72f3f5fc2\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d35d6dd9bef9d08efd5859ca1a973a0\transformed\coil-compose-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\982cff997a1e1a11313719d4a3211ce3\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e64ad488c78ffba5f2022ab4c37bab5d\transformed\coil-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8318b2462cd4d7723a98af038bbde832\transformed\coil-base-2.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2dc4443f8f6abf87451e6993cfdde65\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5264b56fac991336705cb767dfccaed\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6207f00851c2e9d75fdc553849078e25\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4b01eff0b053fa30c37c6057ff408c47\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf05c21af99e4216a15064447bffe658\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe99e2bc9ec26a542322af1ffb30f107\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fc7dec75775111c291eae51985f2182\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e0ae55a1c9a27a242c3ce8c92f7e897\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\9210b2b61ad4ee661fede1a0adfec5b3\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\10065d8502ce57d249742a29b15a8be9\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a3035268b92138243f3c5de6a7d819b\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb3ff91b017c014ea8208b3bab387f88\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b9fee2054cde14b5d0c7ef7c006c665\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e19e676f007de9b37422646cd1c0cc0\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\e79df2c856e716b7414ed0ec9bc2361c\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4805bde63a687339e0bca68a38fea94\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ee20a6d2c4e9f10f9831f86dc29a38\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\91ddaf1a7eddb78b2cf238631f535605\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\03735ffa0c691bd77426b76439f65444\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\109a838557465dce8b961411b7c5c3db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\376a5e7833716fb45044e38d052703f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\480b2bcb8fbbe811cd2ef47e18f7cfa9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff6392be2f1a94faf7022ff1d0f533f3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b21f6d800350111de7e0f375b6bdcd9c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a70492be50d2fc55f3fb9f51b1e6fb52\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1226967a0733362b18fe627e952109f7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdade0dd1f55f748ac86a87a12eb7aca\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a594c2650beaec5f8bc891ed42871a10\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\701b54f0752c7f722e53910e3d6443ea\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6c6672073eee24435f1d87817d5b1d0\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c97fb3e39285217dd66a0ba6b07f3602\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\af12413af43d2d6273e9475aedb00ce5\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c872e72ade1951e256de5fa2183bc3d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12d0172ee037bf0ba76b128de71be868\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\43c2d4e0affb1cc99d3cce8fbdaf8272\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4093d40f74cff40498ca6077a328e6c0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dce735718674b8fbd9411cc4dd923df0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c8f0d79c4f1dd6d68768f6032ce926d1\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\e9d0bd5a97cca118678ba1f16ee80a8c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bfb190c467eca66949acdfb6f6d5938\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:17:1-25:12
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\7bbf6cb3bb06aa9cd4e2e8911ba2b076\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\281a35e110b67217cf7abe7b745e0e93\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ad035627e55d2e7b041d64a53c466e3\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\39fe1bdccf7c4f898a6a43f1d73dabc1\transformed\activity-compose-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c1878b050edb15fe087164aa5725994\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\36cf951654028aaa2f3a8ee4f6ab8f01\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fb57fce86f9209460f125cd70c4080b\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3831760df9c30d3ebeb62a31351e667a\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0df6ce9cce6267dd28186077c513cf2b\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7adb0d05038c3b35aa400d428380b458\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d154730030e4dad86a589301a0de90d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\608974e17025ebb36261908405a66d44\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ea252bd1be7d27cd4639f401612a8c8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb1c92c34cc6075018eeb26b3dfe6366\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:15:1-38:12
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\12c1b66454d006991d81f375d67c2116\transformed\transport-api-2.2.1\AndroidManifest.xml:15:1-22:12
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\769ddb5db186a465851dc3ce888ceca9\transformed\firebase-components-16.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4513177a68cf75fb1ed6ad09f63252b\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b2f74a2d41a7348e020ada5540db6ad\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06363411c7c1df26f8947a4f4604c6c5\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\45da48be99b2efa462a88250273c1a10\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0eecaa27b907c476d79368d378b3cddf\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d1592b9bedc049a5a62b4ed2f6e18f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4a44a150376a2fc761fa3c6f92e5663\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c608419304ef6ebf16304879f204c6\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.CAMERA
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:6:22-62
uses-feature#android.hardware.camera
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:8:5-60
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:8:19-57
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:10:5-83
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:10:22-80
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:11:5-81
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:12:5-80
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:12:22-77
application
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:14:5-45:19
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:14:5-45:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\595ec66ace766be95db48df72f3f5fc2\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\595ec66ace766be95db48df72f3f5fc2\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ea252bd1be7d27cd4639f401612a8c8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ea252bd1be7d27cd4639f401612a8c8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:28:5-36:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:25:5-39:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c608419304ef6ebf16304879f204c6\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c608419304ef6ebf16304879f204c6\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:23:9-35
	android:label
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:21:9-41
	android:fullBackupContent
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:19:9-54
	android:roundIcon
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:22:9-54
	tools:targetApi
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:25:9-29
	android:icon
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:20:9-43
	android:allowBackup
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:17:9-35
	android:theme
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:24:9-41
	android:networkSecurityConfig
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:16:9-69
	android:dataExtractionRules
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:18:9-65
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:15:9-42
activity#com.coffee.erp.MainActivity
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:26:9-34:20
	android:exported
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:28:13-36
	android:theme
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:29:13-45
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:27:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:30:13-33:29
action#android.intent.action.MAIN
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:31:17-69
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:31:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:32:17-77
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:32:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:41:13-43:54
	android:resource
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:43:17-51
	android:name
		ADDED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:42:17-67
uses-sdk
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
MERGED from [OcrLibrary.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b97644a16d8262412c5844c09cb1dad\transformed\OcrLibrary\AndroidManifest.xml:5:5-44
MERGED from [OcrLibrary.aar] C:\Users\<USER>\.gradle\caches\8.9\transforms\8b97644a16d8262412c5844c09cb1dad\transformed\OcrLibrary\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff5604a0cf08c8de7ced63fdd0973655\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff5604a0cf08c8de7ced63fdd0973655\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\54acd67fb2220d5536a3b4d914012cde\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\54acd67fb2220d5536a3b4d914012cde\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9495f7808b4156f18b29808caae1606f\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9495f7808b4156f18b29808caae1606f\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ed2fadd80d6945045f8a5a3b9b067b3\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\2ed2fadd80d6945045f8a5a3b9b067b3\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5be5ade89f44101ec0e491bb2962fd5\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5be5ade89f44101ec0e491bb2962fd5\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\7d72b964d4495d7859d0b78c38bb2d24\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\7d72b964d4495d7859d0b78c38bb2d24\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287a845c912e32b90d9cbfc348ad34a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\b287a845c912e32b90d9cbfc348ad34a\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c05fab69a16c9f81abcb338745b482e\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.7.7] C:\Users\<USER>\.gradle\caches\8.9\transforms\3c05fab69a16c9f81abcb338745b482e\transformed\navigation-compose-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fe45853e0c81b999cf9655bea812e7a\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-systemuicontroller:0.32.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7fe45853e0c81b999cf9655bea812e7a\transformed\accompanist-systemuicontroller-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2081bd59573ce5b579d42acdf887b9b2\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\2081bd59573ce5b579d42acdf887b9b2\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4bee85f02b9349668dbe3bc2f89ef42\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b4bee85f02b9349668dbe3bc2f89ef42\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\00ac1d7733b4dcee47cf92051b0baf0e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\00ac1d7733b4dcee47cf92051b0baf0e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b2c42d0ce84020feb69aadd2f21e791\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b2c42d0ce84020feb69aadd2f21e791\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\96008b571c873a273cfaafc6b8ced783\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\96008b571c873a273cfaafc6b8ced783\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bc541a45dfd0e62233c7bedb463d5b7\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8bc541a45dfd0e62233c7bedb463d5b7\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d5ce7551cf679fefeef255e3a3ff4b9\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d5ce7551cf679fefeef255e3a3ff4b9\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\595ec66ace766be95db48df72f3f5fc2\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\595ec66ace766be95db48df72f3f5fc2\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d35d6dd9bef9d08efd5859ca1a973a0\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d35d6dd9bef9d08efd5859ca1a973a0\transformed\coil-compose-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\982cff997a1e1a11313719d4a3211ce3\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\982cff997a1e1a11313719d4a3211ce3\transformed\coil-compose-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e64ad488c78ffba5f2022ab4c37bab5d\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e64ad488c78ffba5f2022ab4c37bab5d\transformed\coil-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8318b2462cd4d7723a98af038bbde832\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8318b2462cd4d7723a98af038bbde832\transformed\coil-base-2.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2dc4443f8f6abf87451e6993cfdde65\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2dc4443f8f6abf87451e6993cfdde65\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5264b56fac991336705cb767dfccaed\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5264b56fac991336705cb767dfccaed\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6207f00851c2e9d75fdc553849078e25\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6207f00851c2e9d75fdc553849078e25\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4b01eff0b053fa30c37c6057ff408c47\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\4b01eff0b053fa30c37c6057ff408c47\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf05c21af99e4216a15064447bffe658\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\bf05c21af99e4216a15064447bffe658\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe99e2bc9ec26a542322af1ffb30f107\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe99e2bc9ec26a542322af1ffb30f107\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fc7dec75775111c291eae51985f2182\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fc7dec75775111c291eae51985f2182\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e0ae55a1c9a27a242c3ce8c92f7e897\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4e0ae55a1c9a27a242c3ce8c92f7e897\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\9210b2b61ad4ee661fede1a0adfec5b3\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\9210b2b61ad4ee661fede1a0adfec5b3\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\10065d8502ce57d249742a29b15a8be9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\10065d8502ce57d249742a29b15a8be9\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a3035268b92138243f3c5de6a7d819b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6a3035268b92138243f3c5de6a7d819b\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb3ff91b017c014ea8208b3bab387f88\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb3ff91b017c014ea8208b3bab387f88\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b9fee2054cde14b5d0c7ef7c006c665\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b9fee2054cde14b5d0c7ef7c006c665\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e19e676f007de9b37422646cd1c0cc0\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e19e676f007de9b37422646cd1c0cc0\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\e79df2c856e716b7414ed0ec9bc2361c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\e79df2c856e716b7414ed0ec9bc2361c\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4805bde63a687339e0bca68a38fea94\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4805bde63a687339e0bca68a38fea94\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ee20a6d2c4e9f10f9831f86dc29a38\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\34ee20a6d2c4e9f10f9831f86dc29a38\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\91ddaf1a7eddb78b2cf238631f535605\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\91ddaf1a7eddb78b2cf238631f535605\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\03735ffa0c691bd77426b76439f65444\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\03735ffa0c691bd77426b76439f65444\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\109a838557465dce8b961411b7c5c3db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\109a838557465dce8b961411b7c5c3db\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\376a5e7833716fb45044e38d052703f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\376a5e7833716fb45044e38d052703f1\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\480b2bcb8fbbe811cd2ef47e18f7cfa9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\480b2bcb8fbbe811cd2ef47e18f7cfa9\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff6392be2f1a94faf7022ff1d0f533f3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ff6392be2f1a94faf7022ff1d0f533f3\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b21f6d800350111de7e0f375b6bdcd9c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b21f6d800350111de7e0f375b6bdcd9c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a70492be50d2fc55f3fb9f51b1e6fb52\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\a70492be50d2fc55f3fb9f51b1e6fb52\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1226967a0733362b18fe627e952109f7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\1226967a0733362b18fe627e952109f7\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdade0dd1f55f748ac86a87a12eb7aca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\cdade0dd1f55f748ac86a87a12eb7aca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a594c2650beaec5f8bc891ed42871a10\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a594c2650beaec5f8bc891ed42871a10\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\701b54f0752c7f722e53910e3d6443ea\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\701b54f0752c7f722e53910e3d6443ea\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6c6672073eee24435f1d87817d5b1d0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d6c6672073eee24435f1d87817d5b1d0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c97fb3e39285217dd66a0ba6b07f3602\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c97fb3e39285217dd66a0ba6b07f3602\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\af12413af43d2d6273e9475aedb00ce5\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\af12413af43d2d6273e9475aedb00ce5\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c872e72ade1951e256de5fa2183bc3d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0c872e72ade1951e256de5fa2183bc3d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12d0172ee037bf0ba76b128de71be868\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\12d0172ee037bf0ba76b128de71be868\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\43c2d4e0affb1cc99d3cce8fbdaf8272\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\43c2d4e0affb1cc99d3cce8fbdaf8272\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4093d40f74cff40498ca6077a328e6c0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4093d40f74cff40498ca6077a328e6c0\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dce735718674b8fbd9411cc4dd923df0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dce735718674b8fbd9411cc4dd923df0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c8f0d79c4f1dd6d68768f6032ce926d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c8f0d79c4f1dd6d68768f6032ce926d1\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\e9d0bd5a97cca118678ba1f16ee80a8c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\e9d0bd5a97cca118678ba1f16ee80a8c\transformed\lifecycle-viewmodel-compose-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bfb190c467eca66949acdfb6f6d5938\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bfb190c467eca66949acdfb6f6d5938\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:21:5-23:42
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\7bbf6cb3bb06aa9cd4e2e8911ba2b076\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\7bbf6cb3bb06aa9cd4e2e8911ba2b076\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\281a35e110b67217cf7abe7b745e0e93\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\281a35e110b67217cf7abe7b745e0e93\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ad035627e55d2e7b041d64a53c466e3\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ad035627e55d2e7b041d64a53c466e3\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\39fe1bdccf7c4f898a6a43f1d73dabc1\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\39fe1bdccf7c4f898a6a43f1d73dabc1\transformed\activity-compose-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c1878b050edb15fe087164aa5725994\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\4c1878b050edb15fe087164aa5725994\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\36cf951654028aaa2f3a8ee4f6ab8f01\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\36cf951654028aaa2f3a8ee4f6ab8f01\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fb57fce86f9209460f125cd70c4080b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\4fb57fce86f9209460f125cd70c4080b\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3831760df9c30d3ebeb62a31351e667a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\3831760df9c30d3ebeb62a31351e667a\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0df6ce9cce6267dd28186077c513cf2b\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0df6ce9cce6267dd28186077c513cf2b\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7adb0d05038c3b35aa400d428380b458\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7adb0d05038c3b35aa400d428380b458\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d154730030e4dad86a589301a0de90d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8d154730030e4dad86a589301a0de90d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\608974e17025ebb36261908405a66d44\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\8.9\transforms\608974e17025ebb36261908405a66d44\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ea252bd1be7d27cd4639f401612a8c8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\ea252bd1be7d27cd4639f401612a8c8e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb1c92c34cc6075018eeb26b3dfe6366\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fb1c92c34cc6075018eeb26b3dfe6366\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\12c1b66454d006991d81f375d67c2116\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:2.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\12c1b66454d006991d81f375d67c2116\transformed\transport-api-2.2.1\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\769ddb5db186a465851dc3ce888ceca9\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-components:16.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\769ddb5db186a465851dc3ce888ceca9\transformed\firebase-components-16.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4513177a68cf75fb1ed6ad09f63252b\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:17.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e4513177a68cf75fb1ed6ad09f63252b\transformed\firebase-encoders-json-17.1.0\AndroidManifest.xml:19:5-21:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b2f74a2d41a7348e020ada5540db6ad\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2b2f74a2d41a7348e020ada5540db6ad\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06363411c7c1df26f8947a4f4604c6c5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\06363411c7c1df26f8947a4f4604c6c5\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\45da48be99b2efa462a88250273c1a10\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\45da48be99b2efa462a88250273c1a10\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0eecaa27b907c476d79368d378b3cddf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0eecaa27b907c476d79368d378b3cddf\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d1592b9bedc049a5a62b4ed2f6e18f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d1592b9bedc049a5a62b4ed2f6e18f8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4a44a150376a2fc761fa3c6f92e5663\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4a44a150376a2fc761fa3c6f92e5663\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c608419304ef6ebf16304879f204c6\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c608419304ef6ebf16304879f204c6\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
	tools:ignore
		ADDED from [com.google.accompanist:accompanist-drawablepainter:0.30.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5bfb190c467eca66949acdfb6f6d5938\transformed\accompanist-drawablepainter-0.30.1\AndroidManifest.xml:23:9-39
	android:minSdkVersion
		INJECTED from D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0d73df774858ac922a65aba24a5a7635\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40674c7f51de2bd136e18201e4443e6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.coffee.erp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.coffee.erp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
