package com.coffee.erp.view.navigation

import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.coffee.erp.view.inventory.MaterialDetailScreen
import com.coffee.erp.view.inventory.MaterialTransactionLogScreen
import com.coffee.erp.view.material.MaterialFormScreen
import com.coffee.erp.view.material.MaterialListScreen
import com.coffee.erp.view.material.MaterialManagementScreen
import com.coffee.erp.view.scanner.material.MaterialScannerScreen

fun NavGraphBuilder.materialRoutes(navController: NavHostController) {
    // Materials list
    composable(Screen.Materials.route) {
        MaterialListScreen(
            navController = navController,
            onNavigateBack = { navController.popBackStack() },
            onNavigateToMaterialDetail = { materialId ->
                if (materialId > 0) {
                    navController.navigate(Screen.MaterialForm.createRoute(materialId))
                }
            },
            onNavigateToMaterialForm = { navController.navigate(Screen.MaterialForm.route) }
        )
    }

    // Material form to create or edit materials
    composable(
        route = Screen.MaterialForm.route,
        arguments = listOf(
            navArgument("materialId") {
                type = NavType.IntType
                defaultValue = -1
                nullable = false
            },
            navArgument("barcode") {
                type = NavType.StringType
                nullable = true
                defaultValue = null
            }
        )
    ) { backStackEntry ->
        val materialId = backStackEntry.arguments?.getInt("materialId") ?: -1
        val barcode = backStackEntry.arguments?.getString("barcode")

        // 设置动态标题
        val screenTitle = Screen.MaterialForm.getTitle(if (materialId == -1) null else materialId)
        backStackEntry.savedStateHandle["screenTitle"] = screenTitle

        MaterialFormScreen(
            materialId = if (materialId == -1) null else materialId,
            barcode = barcode,
            onNavigateBack = {
                navController.navigate(Screen.Materials.route) {
                    popUpTo(Screen.Materials.route) {
                        inclusive = false
                    }
                    launchSingleTop = true
                }
            },
            onNavigateToScanner = { navController.navigate(Screen.Scanner.route) },
            navController = navController
        )
    }

    // Material detail - showing material transaction records
    composable(
        route = Screen.MaterialDetail.route,
        arguments = listOf(
            navArgument("warehouseId") { type = NavType.IntType },
            navArgument("shelfId") { type = NavType.IntType },
            navArgument("materialId") { type = NavType.IntType }
        )
    ) { backStackEntry ->
        val warehouseId = backStackEntry.arguments?.getInt("warehouseId") ?: 0
        val shelfId = backStackEntry.arguments?.getInt("shelfId") ?: 0
        val materialId = backStackEntry.arguments?.getInt("materialId") ?: 0

        MaterialDetailScreen(
            warehouseId = warehouseId,
            shelfId = shelfId,
            materialId = materialId,
            onNavigateBack = { navController.popBackStack() },
            navController = navController
        )
    }

    // Material transaction log - history of material movements
    composable(
        route = Screen.MaterialTransactionLog.route,
        arguments = listOf(
            navArgument("materialId") { type = NavType.IntType },
            navArgument("warehouseId") {
                type = NavType.IntType
                defaultValue = 0
                nullable = false
            },
            navArgument("shelfId") {
                type = NavType.IntType
                defaultValue = 0
                nullable = false
            }
        )
    ) { backStackEntry ->
        val materialId = backStackEntry.arguments?.getInt("materialId") ?: 0
        val warehouseId = backStackEntry.arguments?.getInt("warehouseId") ?: 0
        val shelfId = backStackEntry.arguments?.getInt("shelfId") ?: 0

        MaterialTransactionLogScreen(
            materialId = materialId,
            warehouseId = warehouseId,
            shelfId = shelfId,
            onNavigateBack = {
                // 当从物料详情页面返回时，如果有仓库和货架信息，则传递回InventoryHierarchy页面
                if (warehouseId > 0 && shelfId > 0) {
                    // 获取前一个页面的savedStateHandle（通常是InventoryHierarchy）
                    val previousEntry = navController.previousBackStackEntry

                    android.util.Log.d(
                        "MaterialTransactionLog",
                        "返回导航 - 仓库ID: $warehouseId, 货架ID: $shelfId, 前一页面路由: ${previousEntry?.destination?.route}"
                    )

                    if (previousEntry?.destination?.route == Screen.InventoryHierarchy.route) {
                        // 设置返回参数到InventoryHierarchy页面的savedStateHandle
                        previousEntry.savedStateHandle.set("returnToWarehouseId", warehouseId)
                        previousEntry.savedStateHandle.set("returnToShelfId", shelfId)

                        android.util.Log.d(
                            "MaterialTransactionLog",
                            "成功设置返回参数到InventoryHierarchy页面"
                        )
                    }
                }
                navController.popBackStack()
            }
        )
    }

    // Material management dashboard
    composable(Screen.MaterialManagement.route) {
        MaterialManagementScreen(
            onNavigateToMaterialForm = { navController.navigate(Screen.MaterialForm.createRoute()) },
            onNavigateToMaterialDetail = { materialId ->
                if (materialId == -1) {
                    // 导航到物料列表页面
                    navController.navigate(Screen.Materials.route)
                } else {
                    // 导航到物料详情页面
                    navController.navigate(Screen.MaterialForm.createRoute(materialId))
                }
            },
            onNavigateToWarehouseManagement = { navController.navigate(Screen.WarehouseManagement.route) },
            onNavigateToDepartmentManagement = { navController.navigate(Screen.DepartmentManagement.route) },
            onNavigateToShelfManagement = {
                navController.navigate(
                    Screen.ShelfManagement.createRoute(
                        null
                    )
                )
            },
            onNavigateToInventoryManagement = { navController.navigate(Screen.InventoryHierarchy.route) },
            onNavigateToInventoryTransaction = { navController.navigate(Screen.InventoryTransaction.route) },
            onNavigateToBorrowRecord = { navController.navigate(Screen.BorrowRecord.route) },
            onNavigateToStockTaking = { navController.navigate(Screen.StockTaking.route) },
            onNavigateToScanner = { navController.navigate(Screen.MaterialScanner.route) },
            navController = navController
        )
    }

    // Material scanner
    composable(Screen.MaterialScanner.route) {
        MaterialScannerScreen(
            navController = navController,
            onNavigateBack = { navController.popBackStack() }
        )
    }
} 