1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.coffee.erp"
4    android:versionCode="125"
5    android:versionName="1.2.5" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:6:5-65
12-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:6:22-62
13
14    <uses-feature android:name="android.hardware.camera" />
14-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:8:5-60
14-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:8:19-57
15
16    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
16-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:10:5-83
16-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:10:22-80
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:11:5-81
17-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:11:22-78
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:12:5-80
18-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:12:22-77
19
20    <permission
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
21        android:name="com.coffee.erp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
21-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
22        android:protectionLevel="signature" />
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
23
24    <uses-permission android:name="com.coffee.erp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
25    <!-- <uses-sdk android:minSdkVersion="14"/> -->
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
26-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
27
28    <application
28-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:14:5-45:19
29        android:name="com.coffee.erp.CoffeeApplication"
29-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:15:9-42
30        android:allowBackup="true"
30-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:17:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e5d73adcfb17213c82d856cf61ade8ee\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:18:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:19:9-54
36        android:icon="@mipmap/ic_launcher"
36-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:20:9-43
37        android:label="@string/app_name"
37-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:21:9-41
38        android:networkSecurityConfig="@xml/network_security_config"
38-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:16:9-69
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:22:9-54
40        android:supportsRtl="true"
40-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:23:9-35
41        android:theme="@style/Theme.ERP" >
41-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:24:9-41
42        <activity
42-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:26:9-34:20
43            android:name="com.coffee.erp.MainActivity"
43-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:27:13-41
44            android:exported="true"
44-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:28:13-36
45            android:theme="@style/Theme.ERP" >
45-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:29:13-45
46            <intent-filter>
46-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:30:13-33:29
47                <action android:name="android.intent.action.MAIN" />
47-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:31:17-69
47-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:31:25-66
48
49                <category android:name="android.intent.category.LAUNCHER" />
49-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:32:17-77
49-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:32:27-74
50            </intent-filter>
51        </activity>
52
53        <provider
54            android:name="androidx.core.content.FileProvider"
54-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:37:13-62
55            android:authorities="com.coffee.erp.fileprovider"
55-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:38:13-64
56            android:exported="false"
56-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:39:13-37
57            android:grantUriPermissions="true" >
57-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:40:13-47
58            <meta-data
58-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:41:13-43:54
59                android:name="android.support.FILE_PROVIDER_PATHS"
59-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:42:17-67
60                android:resource="@xml/file_paths" />
60-->D:\workspace\gitee.com\coffee\ERP\webadmin\ERP\android\ERP\app\src\main\AndroidManifest.xml:43:17-51
61        </provider>
62
63        <service
63-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
64            android:name="androidx.camera.core.impl.MetadataHolderService"
64-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
65            android:enabled="false"
65-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
66            android:exported="false" >
66-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
67            <meta-data
67-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
68                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
68-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
69                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
69-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\352a4d75a83fb1271ff2c09ae5c5408e\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
70        </service>
71        <service
71-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
72            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
72-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
73            android:directBootAware="true"
73-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
74            android:exported="false" >
74-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
75            <meta-data
75-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
76                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
76-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
77                android:value="com.google.firebase.components.ComponentRegistrar" />
77-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\518c4fad878bee27837602cd6ff710cf\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
78            <meta-data
78-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
79                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
79-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
80                android:value="com.google.firebase.components.ComponentRegistrar" />
80-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c42fd8ff455379704b6bbcafbfe07253\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
81            <meta-data
81-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
82                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
82-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
83                android:value="com.google.firebase.components.ComponentRegistrar" />
83-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
84        </service>
85
86        <provider
86-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
87            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
87-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
88            android:authorities="com.coffee.erp.mlkitinitprovider"
88-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
89            android:exported="false"
89-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
90            android:initOrder="99" />
90-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\07c040e66286e66505ca62aec838c8d3\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
91
92        <activity
92-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
93            android:name="com.google.android.gms.common.api.GoogleApiActivity"
93-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
94            android:exported="false"
94-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
95            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
95-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7103e3a45d5e77488042818e8a775ede\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
96
97        <meta-data
97-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
98            android:name="com.google.android.gms.version"
98-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
99            android:value="@integer/google_play_services_version" />
99-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1848cd7ef0ac74c441d15ac0624b38f8\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
100
101        <activity
101-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
102            android:name="androidx.compose.ui.tooling.PreviewActivity"
102-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
103            android:exported="true" />
103-->[androidx.compose.ui:ui-tooling-android:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a7609f1f9c97c73b5c6cd182b85f728b\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
104
105        <provider
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
106            android:name="androidx.startup.InitializationProvider"
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
107            android:authorities="com.coffee.erp.androidx-startup"
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
108            android:exported="false" >
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
109            <meta-data
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.emoji2.text.EmojiCompatInitializer"
110-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
111                android:value="androidx.startup" />
111-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d52afd6a1647318834bffffd864b12b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
112            <meta-data
112-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
113                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
113-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
114                android:value="androidx.startup" />
114-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\42473832e2e003ccdffad3b80ea7e113\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
115            <meta-data
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
116                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
117                android:value="androidx.startup" />
117-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
118        </provider>
119
120        <activity
120-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:23:9-25:39
121            android:name="androidx.activity.ComponentActivity"
121-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:24:13-63
122            android:exported="true" />
122-->[androidx.compose.ui:ui-test-manifest:1.6.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\a0c292d116ec7f25ad1a8636a967f01b\transformed\ui-test-manifest-1.6.6\AndroidManifest.xml:25:13-36
123
124        <service
124-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
125            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
125-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
126            android:exported="false" >
126-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
127            <meta-data
127-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
128                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
128-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
129                android:value="cct" />
129-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\64d7bb40a1fed24affb799329339b362\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
130        </service>
131        <service
131-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
132            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
132-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
133            android:exported="false"
133-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
134            android:permission="android.permission.BIND_JOB_SERVICE" >
134-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
135        </service>
136
137        <receiver
137-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
138            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
138-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
139            android:exported="false" />
139-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\8ff7d72db5eab3d4c40facde6f75a892\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\18000fc76f0f3e2b549a821d33224f64\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
